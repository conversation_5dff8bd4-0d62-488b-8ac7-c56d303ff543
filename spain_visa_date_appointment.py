import json
import requests
from extension.logger import logger
from bs4 import BeautifulSoup
from datetime import datetime
import os
import random
import time
from copy import deepcopy

from tool import update_booking_status, BOOK_STATUS
from config import url_host, wx_hook_urls
from tool import get_user_head_img, send_dd_msg, area_map, extract_alert_msg, save_pay_page_2_temp, send_wx_msg
from user_manager import get_email_otp, get_users_with_queue_name, save_user_2_redis_queue, move_user_2_success_queue, delete_email_otp, move_user_2_queue
from user_manager import spain_error_users, spain_user_field, spain_success_users, get_user_info
from spain_visa_appointment_date_open import book_appointment
from bypass_date_appointment import bypass_date_appointment_verify, extract_slot_time_link, pick_available_day, extract_func_params

url_verify_email = "/CHN/blsappointment/VerifyEmail"


# 微信通知付款信息
def send_payment_msg(user={}):
    try:
        pno = f"护照号:{user.get('passportNO')}"
        user_name = f"客户姓名:{user.get('chnname')}"
        appointment_area = f"地区:{area_map[user.get('centerCode')]}"
        user_mail = f"邮箱:{user['email']}"
        url = random.choice(wx_hook_urls)
        app_id = f"预约信ID:{user.get('appointment_id')}"
        app_day = f"日期:{user.get('appointment_day')}"
        app_time = f"时间:{user.get('appointment_time')}"
        remark = f"备注:{user.get('remark')}"
        visa_type = f"{user['visaTypeCode']}"
        u_from = f"from：{user.get('from')}"
        reg = f"登记：{user.get('reg')}"
        msg_string = "#西班牙客户预约成功, 等待付款#" + " | ".join([user_name, user_mail, pno, visa_type, appointment_area, app_id, app_day, app_time, u_from, reg, remark])
        logger.success(msg_string)
        if user.get("queue_name") in [spain_user_field, spain_success_users]:
            # 钉钉通知
            send_dd_msg(msg_string)
            # 微信通知
            requests.post(url, json={"msgtype": "text", "text": {"content": msg_string, "mentioned_mobile_list": ["15900597586"]}}, timeout=5)
    except Exception as e:
        logger.error(f"#预约付款通知错误#{user.get('email')}-{user.get('passportNO')}-{user.get('password')} Err:{e.args[0]}")
        send_dd_msg("#西班牙客户预约成功, 等待付款#" + f"{user.get('email')}-{user.get('passportNO')}-{user.get('password')}-{user.get('appointment_id')}")


# 保存付款页面到本地
def save_pay_page_2_local(html_string, p_NO="", email=""):
    # 本地保存付款html页面
    try:
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        save_path = os.path.join("pay_users", f"{date_str}_{email}_{p_NO}") + ".html"
        with open(save_path, "w", encoding="utf-8") as fw:
            fw.write(html_string)
    except Exception:
        logger.error(f"#预约中# {email}:{p_NO}保存付款页面到本地错误")


def extract_app_no(res_pay_page, user_info):
    try:
        app_no = ""
        payed_soup = BeautifulSoup(res_pay_page, "html.parser")
        ul_els = payed_soup.select(".list-group-item.d-flex.justify-content-between.pb-3")
        for li in ul_els:
            span_els = li.find_all("span")
            title = span_els[0]
            value = span_els[1]
            if title.text.startswith("Appointment No"):
                app_no = value.text
                break
        if app_no == "":
            # 没解析成功就保存下看看
            logger.warning(f"##预约中## 解析Appointment No失败: {user_info.get('chnname')} {user_info.get('email')} {user_info.get('passportNO')}")
            save_pay_page_2_temp(res_pay_page, "-ERROR-" + user_info.get("passportNO"), user_info.get("email"))
        return app_no
    except Exception as e:
        # 没解析成功就保存下看看
        logger.warning(f"##预约中## 解析Appointment No失败: {user_info.get('chnname')} {user_info.get('email')} {user_info.get('passportNO')}, {e}")
        save_pay_page_2_temp(res_pay_page, "-ERROR-" + user_info.get("passportNO"), user_info.get("email"))
        return ""


def date_appointment(user_info: dict, session: requests.Session):
    try:
        user_log_info = f"{user_info.get('chnname')}-{user_info.get('centerCode')}-{user_info.get('passportNO')}-{user_info.get('visaTypeCode')}-{user_info.get('dateVIP')}-{user_info.get('email')}"
        # user_cn_name = user_info.get("chnname")
        t_s = time.time()
        # img_head_bytes = get_user_head_img(user_info)  # user_info["passportNO"]
        # if not img_head_bytes:  # 没拿到照片
        #     logger.error(f"#预约中# 用户头像图片未上传: {user_info.get('chnname')} {user_info.get('passportNO')} {user_info.get('email')}")
        #     return False, False
        seach_flag, respone_data = book_appointment(user_info, session)
        logger.warning(f"#预约中# 选类型耗时:{round(time.time()-t_s, 2)}s, {user_log_info}")
        if not seach_flag:
            logger.error(f"#预约中# 查询放号流程失败。{user_log_info}")
            return False, False
        if not respone_data.get("success"):
            logger.warning(f"#预约中# 查询放号结果未开放。{user_log_info}: {respone_data.get('message')}")
            return False, respone_data  # 20250709调整
        choose_date_html = respone_data.get("html")
        open_days = respone_data.get("days")
        if not choose_date_html:
            logger.error(f"#预约中# 查询放号流程失败。{user_log_info}")
            return False, seach_flag  # 20250709调整
        # 选择符合用户预期的日期
        available_days = pick_available_day(user_info, open_days)
        if len(available_days) == 0:
            logger.warning(f"#预约中# 暂无用户期望的可预约日期 {user_log_info}")
            return False, respone_data  # 20250709调整

        # 根据日期请求当天可预约的时间段
        slot_time_uri = extract_slot_time_link(choose_date_html)
        if not slot_time_uri:
            logger.error("#预约中# 未获取到时间段请求链接 /CHN/appointment/GetAvailableSlotsByDate")
            return False, respone_data  # 20250709调整

        date_soup = BeautifulSoup(choose_date_html, "html.parser")
        day_key, slot_key, res_data, uri_post_date = bypass_date_appointment_verify(date_soup)
        form_dict_date = {_.get("name"): _.get("value") for _ in date_soup.select("input")}
        session.headers.update({"requestverificationtoken": form_dict_date.get("__RequestVerificationToken"), "x-requested-with": "XMLHttpRequest"})
        # 选时间段
        slot_time_id = None
        slot_day = None
        slot_time_string = None
        for day_time in available_days:
            day_time = random.choice(available_days)
            # 获取全部时间段
            url_date_slot = url_host + slot_time_uri + day_time
            t_s = time.time()
            res_time = session.post(url_date_slot, verify=False, timeout=50)
            logger.warning(f"选时间耗时: {round(time.time()-t_s, 2)} s, {user_log_info}")
            if res_time.status_code != 200 or not res_time.json().get("success"):
                # update_booking_status(user_info, BOOK_STATUS.SCHEDULE_ERROR, "预约时间段选择失败")
                logger.error(f"#预约中# 获取预约时间段出错 {user_log_info} {res_time.status_code}:{url_date_slot}")
                return False, False
            # 可预约的时间段 eg: 8:30 - 9:00 选最多的一个
            open_times = list(filter(lambda x: x.get("Count", 0) != 0, res_time.json().get("data", [])))
            # open_times = sorted(open_times, key=lambda x: x["Count"], reverse=True)
            if len(open_times):
                open_time = random.choice(open_times)  # open_times[0]  # random.choice(open_times)
                slot_time_id = open_time.get("Id")  # 随机选个时间
                slot_time_string = open_time.get("Name")
                slot_day = day_time
                break
            else:
                continue

        if not slot_day or not slot_time_id:
            logger.error(f"#预约中#  没有选到合适的日期 {user_log_info} {slot_day} {slot_time_string}")
            # update_booking_status(user_info, BOOK_STATUS.SCHEDULE_ERROR, "没有符合预期的时间")
            return False, False

        info_dict = {}
        info_dict[day_key] = slot_day
        info_dict[slot_key] = slot_time_id

        res_data.update(info_dict)
        form_dict_date.update(info_dict)
        form_dict_date["ResponseData"] = json.dumps(res_data)
        selected_applicant, _ = extract_func_params(choose_date_html, "OnApplicantSelect")
        form_dict_date["SelectedApplicants"] = selected_applicant
        # /CHN/Appointment/slotSelection
        url_post_date = url_host + uri_post_date
        # 确认选择的日期，重定向到 /CHN/Appointment/ApplicantSelection
        delete_email_otp(user_info.get("email"))  # 这个接口会发送邮箱验证码，发送之前先删除一遍，防止取错
        t_s = time.time()
        # 确认日期和时间段
        for _ in range(3):
            res_slot = session.post(url_post_date, data=form_dict_date, verify=False, timeout=40)
            if res_slot.status_code == 502:
                continue
            else:
                break

        logger.warning(f"确认时间耗时: {round(time.time()-t_s, 2)}s, {user_log_info}")
        if res_slot.status_code != 200:
            logger.error(f"#预约中#  SlotSelection选日期表单提交错误: {user_log_info} | {form_dict_date} | {res_slot.status_code} | {res_slot.url}")
            return False, False

        # 已经有预约了,包括预约流程错误发生中端的， 需要取消重新开始
        if "/CHN/appointment/pendingappointment".upper() in res_slot.url.upper():
            res_pending = session.post(url_host + "/CHN/Appointment/PendingAppointment", verify=False, timeout=50)
            msg = extract_alert_msg(res_pending.text)
            if res_pending.status_code == 200:
                res_visa_type = session.get(url_host + "/CHN/appointment/newappointment", verify=False, timeout=15)
                logger.warning(f"#预约# 取消结果:{res_visa_type.status_code}")
            logger.warning(f"#预约# 取消正在进行的预约。 {user_log_info} {msg} {res_pending.status_code}, {res_pending.url}")
            # 记录重试次数，超过三次删除重建账号
            user_info["date_retry"] = int(user_info.get("date_retry", 0)) + 1
            # 大于三次重试注册账户
            if int(user_info["date_retry"]) % 3 == 0:
                logger.warning(f"#预约# 预约失败超过三次,重新注册尝试。 {user_log_info} {msg} {res_pending.status_code}, {res_pending.url}")
                if int(user_info["date_retry"]) < 6:
                    user_info["status"] = "delete_2_registe"
                    user_delete = deepcopy(user_info)
                    user_delete["queue_name"] = spain_error_users
                    save_user_2_redis_queue(user_delete)  # 另存一份到错误队列等待删除
                else:
                    user_info["status"] = "pause"
                    send_wx_msg(f"#西班牙暂停用户# {user_log_info}")
                    send_dd_msg(f"#西班牙暂停用户# {user_log_info}")
                    # save_user_2_redis_queue(user_info)
                # move_user_2_queue(user_info, spain_error_users)
            return False, False

        # if not selected_applicant:
        # 重定向到邮箱确认码页面 上传头像
        if "/CHN/Appointment/ApplicantSelection".upper() in res_slot.url.upper():
            email_verify_code = get_email_otp(user_info["email"])
            if not email_verify_code:
                logger.error(f"##预约中## 获取邮箱验证码超时:{user_log_info}")
                return False, False

            retry_url = res_slot.url
            image_id = None  # "0a23d831-34bb-4509-8b90-eae12948636c"
            # 打开下面这句代码会直接复用注册时候传的头像。后面修改了头像可能不会更新。
            image_id = user_info.get("image_id")
            for retry_count in range(3):
                if retry_count > 0:  # 上传头像页面刷新重试
                    res_slot = session.get(retry_url, verify=False, timeout=50)
                application_soup = BeautifulSoup(res_slot.text, "html.parser")
                form_dict_app = {_.get("name"): _.get("value") for _ in application_soup.select("input") if _.get("name")}
                selected_applicant, _ = extract_func_params(res_slot.text, "OnApplicantSelect")
                # 如果有image_id就直接跳过上传头像
                if image_id:
                    break
                # part 上传头像
                try:
                    user_refresh = get_user_info(user_info)  # 刷新头像
                    avatar_images = user_refresh.get("avatar_images")
                    if not avatar_images:
                        avatar_images = [user_refresh.get("avatar_image")]  # 兼容旧数据
                    for avatar_image in avatar_images:
                        img_head_bytes = get_user_head_img(user_info, avatar_image)  # user_info["passportNO"]
                        if not img_head_bytes:  # 没拿到照片
                            logger.error(f"#预约中# 用户头像图片未上传: {user_info.get('chnname')} {user_info.get('passportNO')} {user_info.get('email')}")
                            # return False, False
                            # continue
                        session.headers.update({"requestverificationtoken": form_dict_app.get("__RequestVerificationToken"), "x-requested-with": "XMLHttpRequest"})
                        time_start = time.time()
                        res_upload = session.post(f"{url_host}/CHN/appointment/UploadApplicantPhoto", files={"file": (f"{user_info['passportNO']}.png", img_head_bytes, "image/jpeg")})
                        logger.warning(f"上传头像耗时: {round(time.time()-time_start, 2)} s, {user_log_info}")
                        if res_upload.status_code == 200 and res_upload.json()["success"]:
                            user_info["image_id"] = res_upload.json()["fileId"]
                            image_id = res_upload.json()["fileId"]
                            save_user_2_redis_queue(user_info)
                            break
                        else:
                            user_info.pop("image_id", None)
                            err_log = f"#预约中# 用户照片上传失败，重试第{retry_count+1}次:{user_info.get('chnname')}-{user_info.get('passportNO')} {user_info.get('email')}, {res_upload.status_code}:{res_upload.text}"
                            logger.warning(err_log)
                    if image_id:
                        break
                    else:
                        err_log = f"#预约中# 用户照片上传失败，重试第{retry_count+1}次:{user_info.get('chnname')}-{user_info.get('passportNO')} {user_info.get('email')}"
                        send_dd_msg(err_log)
                        continue
                except Exception as e:
                    logger.error(f"#预约中# 用户照片上传错误:{user_info.get('email')}-{user_info.get('passportNO')}, error:{e.args[0]}")
                    return False, False

            # 重试多次还是上传失败
            if not image_id:
                err_log = f"#预约中# 用户照片上传3次失败:{user_info.get('chnname')}-{user_info.get('passportNO')} {user_info.get('email')}, {res_upload.status_code}:{res_upload.text}"
                logger.error(err_log)
                send_dd_msg(err_log)
                send_wx_msg(err_log)
                update_booking_status(user_info, BOOK_STATUS.SCHEDULE_ERROR, "用户头像上传失败:" + res_upload.status_code)
                return False, False

            form_dict_app["ApplicantPhotoId"] = image_id  # "5c44a9cd-7b5e-42c9-abbb-c05e62785619"
            for _ in range(3):
                form_dict_app["EmailCode"] = email_verify_code
                form_dict_app["SelectedApplicants"] = selected_applicant
                form_dict_app["TravelDate"] = str(user_info.get("travelDate")).replace("/", "-")[:10]
                form_dict_app["ServerTravelDate"] = str(user_info.get("travelDate")).replace("/", "-")[:10]

                applicant_post_uri = "/CHN/Appointment/ApplicantSelection"
                if application_soup.find("form"):
                    applicant_post_uri = application_soup.find("form").get("action", "")

                applicant_post_url = url_host + applicant_post_uri
                t_s = time.time()
                applicat_res = session.post(applicant_post_url, data=form_dict_app, verify=False, timeout=40)
                logger.warning(f"##预约中## 确认信息耗时: {round(time.time()-t_s, 2)}s, {user_log_info}")
                if applicat_res.status_code in [500, 502, 503, 504]:
                    continue
                if applicat_res.status_code != 200:
                    err_msg = extract_alert_msg(applicat_res.text)
                    logger.error(f"##预约中## ApplicantSelection1 表单提交错误:{user_log_info} {err_msg} {applicat_res.status_code}:{applicat_res.url}")
                    return False, False
                # 重新获取email OTP
                if applicant_post_uri.upper() in applicat_res.url.upper():
                    email_verify_code = get_email_otp(user_info["email"])
                    if not email_verify_code:
                        logger.error(f"##预约中## 获取邮箱验证码超时:{user_log_info}")
                        return False, False
                    continue
                else:
                    break
        elif "/CHN/Appointment/Payment".upper() in res_slot.url.upper():  # 直接跳转到付款页面
            applicat_res = res_slot
        else:
            alert_msg = ""
            try:
                alert_el = extract_alert_msg(res_slot.text)
                if alert_el:
                    alert_msg = user_log_info + " 预约错误: " + alert_el
                    send_dd_msg(alert_msg)
                    # send_wx_msg(alert_msg)
                    if "Maximum number of appointments are booked from your given email domain" == alert_el:
                        # 移动到重新注册队列
                        user_info["status"] = "delete_2_registe"
                        user_delete = deepcopy(user_info)
                        user_delete["queue_name"] = spain_error_users
                        save_user_2_redis_queue(user_delete)  # 另存一份到错误队列等待删除
            except Exception as e:
                logger.error(f"#预约中#  解析错误页面错误:{e}")
            logger.error(f"#预约中#  选日期表单提交错误: {alert_msg} {res_slot.status_code} {res_slot.url}")
            return False, False

        if "/CHN/Appointment/Payment".upper() not in applicat_res.url.upper():
            err_msg = extract_alert_msg(applicat_res.text)
            logger.error(f"##预约中## ApplicantSelection2 表单提交错误:{user_log_info} {err_msg} {form_dict_app} {applicat_res.status_code}:{applicat_res.url}:{applicat_res.text[-300:]}")
            return False, False

        save_pay_page_2_temp(applicat_res.text, user_info.get("passportNO"), user_info.get("email"))

        # 确认付款
        soup_payment = BeautifulSoup(applicat_res.text, "html.parser")
        form_pay_dict = {_.get("name"): _.get("value") for _ in list(filter(lambda x: x.attrs.get("type") == "hidden", soup_payment.select("input")))}
        ValueAddedServices = list(filter(lambda x: x.attrs.get("checked") == "checked", soup_payment.select("input")))[0].attrs.get("id")
        ValueAddedServices = ValueAddedServices.split("chk_")[1] + "_1"  # 默认付款金额 数量1
        form_pay_dict["ValueAddedServices"] = ValueAddedServices
        form_pay_dict["X-Requested-With"] = "XMLHttpRequest"
        pay_way = 2 if len(soup_payment.select(".nav-pills-primary-soft")) else "BaridCash"
        form_pay_dict["PaymentGatewayType"] = pay_way

        logger.warning(f"#预约中# 付款方式:{pay_way}")

        # 确认付款方式
        uri_pay_confirm = "/CHN/payment/PaymentRequest"
        if soup_payment.find("form"):
            uri_pay_confirm = soup_payment.find("form").get("action", "")
            # 确认付款方式
        url_pay_confirm = url_host + uri_pay_confirm

        session.headers.update({"content-type": "application/x-www-form-urlencoded; charset=UTF-8"})
        for _ in range(3):
            res_pay_confirm = session.post(url_pay_confirm, data=form_pay_dict, verify=False, timeout=30)
            if res_pay_confirm.status_code in [502, 503, 500]:
                time.sleep(0.1)
                continue
            else:
                break
        if res_pay_confirm.status_code != 200 or not res_pay_confirm.json().get("success"):
            logger.error(f"##预约失败##付款方式确认请求出错,{user_info.get('email')}-{user_info.get('centerCode')}-{user_info.get('passportNO')}, code:{res_pay_confirm.status_code}, text:{res_pay_confirm.text}")
            return False, False
        # 拿到付款的二维码或者直接告知成功（现金支付）
        payType = res_pay_confirm.json().get("payType")
        pay_way_map = {2: "微信", 3: "支付宝", 4: "银联", "BaridCash": "现金"}
        if payType in [2, 3, "2", "3"]:
            logger.info(f"##预约付款方式选择##{user_info['email']}-{user_info.get('passportNO')}, 付款方式:{pay_way_map.get(payType)}")
            idData = res_pay_confirm.json().get("id")
            url_pay_page = url_host + "/CHN/payment/paymentqr?idData=" + idData
            res_pay_page = session.post(url_pay_page, verify=False, timeout=15)
            if res_pay_page.status_code != 200:
                logger.error(f"##预约失败##待付款页请求出错,{user_info.get('email')}-{user_info.get('centerCode')}-{user_info.get('passportNO')}, code:{res_pay_page.status_code}, text:{res_pay_page.text[-200:]}")
                return False, False

            # 本地保存付款html页面
            save_pay_page_2_local(res_pay_page.text, user_info["passportNO"], user_info["email"])
            # 保存付款二维码到redis，等待付款
            soup = BeautifulSoup(res_pay_page.text, "html.parser")
            el_imgs = soup.find_all("img")
            # 找到页面中付款码的base64
            for el_img in el_imgs:
                if el_img.attrs.get("src").startswith("data:image/jpeg;base64"):
                    user_info["pay_qrcode"] = el_img.attrs.get("src")  # 保存付款二维码
                    user_info["status"] = "payment"  # 修改用户状态为待付款
                    user_info["appointment_id"] = form_pay_dict.get("Id")  # 下载预约信的ID
                    user_info["appointment_day"] = slot_day  # 预约的日期
                    user_info["appointment_time"] = slot_time_string  # 预约的时间
                    user_info["pay_time"] = str(time.time())  # 预约成功的时间
                    user_info["idData"] = idData  # 付款完成发请求与系统同步
                    # save_user_2_redis_queue(user_info)
                    move_user_2_success_queue(user_info)
                    send_payment_msg(user_info)
                    break
            update_booking_status(user_info, BOOK_STATUS.WAITING_FOR_PAYMENT, "预约成功，等待付款")
            logger.success(f"##预约成功## {user_info.get('email')}-{user_info.get('centerCode')}-{user_info.get('passportNO')}, 预约信ID:{form_pay_dict.get('Id')}, idData:{idData}")
            return True, True
        elif payType in ["0", 0]:  # 现金支付直接预约成功
            url_pay_page = url_host + res_pay_confirm.json().get("requestURL")
            res_pay_page = session.get(url_pay_page, verify=False, timeout=15)
            if res_pay_page.status_code != 200:
                logger.error(f"#预约中# 待付款页请求出错,{user_info.get('email')}-{user_info.get('centerCode')}-{user_info.get('passportNO')}, code:{res_pay_page.status_code}, text:{res_pay_page.text}")
                return False, False

            # 本地保存付款html页面
            save_pay_page_2_local(res_pay_page.text, user_info.get("passportNO"), user_info.get("email"))
            app_no = extract_app_no(res_pay_page.text, user_info)
            # if "Your appointment has been booked successfully" in res_pay_page.text:
            user_info["status"] = "payed"  # 修改用户状态为已完成
            user_info["appointment_id"] = form_pay_dict.get("Id")  # 下载预约信的ID
            user_info["appointment_no"] = app_no  # 取消预约的ID
            user_info["appointment_day"] = slot_day
            user_info["appointment_time"] = slot_time_string
            user_info["pay_time"] = str(time.time())
            # save_user_2_redis_queue(user_info)
            move_user_2_success_queue(user_info)
            logger.success(f"##预约成功## {user_info.get('email')}-{user_info.get('centerCode')}-{user_info.get('passportNO')}, appointment_id:{form_pay_dict.get('Id')}")
            return True, True

        return False, False
    except Exception as e:
        logger.error(f"##预约失败##流程出错：{user_log_info},error:{e.args[0]}")
        return False, e.args[0]


if __name__ == "__main__":
    all_users = get_users_with_queue_name()
    # 预约信下载链接
    "https://spain.blscn.cn/CHN/Payment/GetAppointmentLetterByAppointmentId?appointmentId=c228ad2a-b573-4b94-9ac5-ead290c69713"
