#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版西班牙签证用户登录模块 V2
主要优化点：
1. 线程隔离 - 每个线程独立处理用户，避免相互影响
2. 实时同步 - 用户队列与Redis状态实时同步
3. 资源管理 - 优化代理和会话管理
4. 错误处理 - 完善的重试和异常处理机制
"""

from curl_cffi import requests
import time
import threading
from queue import Queue, Empty
from copy import deepcopy
from collections import defaultdict
from typing import Dict, Set, Optional, List

from spain_visa_login import user_login
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent
from config import headers
from user_manager import save_user_2_redis_queue, spain_user_field, redis_client, hget_redis_user, get_user_info, get_user_date_status, set_user_date_status


class ThreadSafeUserManager:
    """线程安全的用户管理器"""

    def __init__(self):
        self.redis_cli = redis_client.client
        self.user_locks = defaultdict(threading.RLock)  # 每个用户一个锁
        self.global_lock = threading.RLock()
        self.processed_users = set()  # 记录正在处理的用户

    def get_user_lock(self, user_field: str) -> threading.RLock:
        """获取用户专用锁"""
        with self.global_lock:
            return self.user_locks[user_field]

    def is_user_processing(self, user_field: str) -> bool:
        """检查用户是否正在被处理"""
        with self.global_lock:
            return user_field in self.processed_users

    def mark_user_processing(self, user_field: str, processing: bool = True):
        """标记用户处理状态"""
        with self.global_lock:
            if processing:
                self.processed_users.add(user_field)
            else:
                self.processed_users.discard(user_field)

    def get_redis_users(self) -> Set[str]:
        """获取Redis中的所有用户字段"""
        try:
            return set(self.redis_cli.hkeys(spain_user_field))
        except Exception as e:
            logger.error(f"获取Redis用户列表失败: {e}")
            return set()


class UserLoginWorker:
    """独立的用户登录工作器"""

    def __init__(self, worker_id: int, user_manager: ThreadSafeUserManager, sync_manager=None):
        self.worker_id = worker_id
        self.user_manager = user_manager
        self.sync_manager = sync_manager  # 用于更新统计信息
        self.stats = {"processed": 0, "success": 0, "failed": 0, "skipped": 0}
        self.running = True

    def should_process_user(self, user: dict) -> bool:
        """判断用户是否需要处理"""
        if not user:
            return False

        # 检查用户状态
        if user.get("status") != "update_appointment":
            return False

        # 检查是否正在预约
        if get_user_date_status(user):
            return False

        # 检查登录状态和时间
        if user.get("is_login", False):
            update_time = int(user.get("updateTime", 0))
            if int(time.time()) - update_time <= 600:  # 10分钟内有效
                return False

        return True

    def login_user_safe(self, user_field: str) -> Optional[dict]:
        """线程安全的用户登录处理"""
        user_lock = self.user_manager.get_user_lock(user_field)

        with user_lock:
            try:
                # 标记用户正在处理
                self.user_manager.mark_user_processing(user_field, True)

                # 从Redis获取最新用户信息
                user = hget_redis_user(user_field, spain_user_field)
                if not user:
                    logger.debug(f"Worker-{self.worker_id}: 用户不存在 {user_field}")
                    self.stats["skipped"] += 1
                    return None

                # 检查是否需要处理
                if not self.should_process_user(user):
                    self.stats["skipped"] += 1
                    return user

                # 执行登录
                start_time = time.time()
                result = self.perform_login(user)
                process_time = time.time() - start_time

                self.stats["processed"] += 1

                # 更新队列统计信息
                if self.sync_manager:
                    self.sync_manager.update_queue_stats(self.worker_id, process_time)

                log_msg = f"{user.get('chnname')} {user.get('centerCode')} {user.get('visaTypeCode')} {user.get('acceptVIP')} {user.get('email')} {user.get('startDate')} {user.get('endDate')}"
                if result and result.get("is_login", False):
                    self.stats["success"] += 1
                    logger.success(f"#登录成功# w-{self.worker_id} {log_msg} (耗时: {process_time:.2f}s)")
                else:
                    self.stats["failed"] += 1
                    logger.error(f"#登录失败# {log_msg} w-{self.worker_id}: (耗时: {process_time:.2f}s)")

                return result

            except Exception as e:
                logger.error(f"Worker-{self.worker_id}: 登录处理异常 {user_field}, {e}")
                self.stats["failed"] += 1
                return None
            finally:
                # 取消处理标记
                self.user_manager.mark_user_processing(user_field, False)

    def perform_login(self, user: dict) -> Optional[dict]:
        """执行实际的登录操作"""
        try:
            # 创建会话
            header = deepcopy(headers)
            header["user-agent"] = get_random_user_agent()
            session = requests.Session(impersonate="chrome131")
            session.headers = header

            # 获取代理
            proxy = get_new_proxy(faker=False)
            if not proxy:
                logger.warning(f"Worker-{self.worker_id}: 无可用代理 {user.get('email')}")
                return user

            proxy_dict = {"http": proxy, "https": proxy}
            session.proxies.update(proxy_dict)

            logger.debug(f"Worker-{self.worker_id}: 正在登录 {user.get('email')}")

            # 执行登录
            flag_login, info_login = user_login(user, session)

            # 更新用户信息
            fresh_user = get_user_info(user)  # 获取最新用户信息
            if not fresh_user:
                fresh_user = user

            if flag_login:
                # 登录成功
                cookie_dict = session.cookies.get_dict()
                fresh_user["dest"] = "0cf9da11-cb76-4566-81e5-8628d5488e3c"
                fresh_user["cookies"] = cookie_dict
                fresh_user["proxy"] = proxy_dict["http"]
                fresh_user["is_login"] = True
                fresh_user["updateTime"] = int(time.time())
            else:
                # 登录失败
                fresh_user["is_login"] = False

            # 更新Redis
            if user['passportNO'] == '*********':
                logger.info(user)
            set_user_date_status(fresh_user, False)
            save_user_2_redis_queue(fresh_user)

            return fresh_user

        except Exception as e:
            logger.error(f"Worker-{self.worker_id}: 登录执行异常 {user.get('email')}, {e}")
            return user

    def get_stats(self) -> dict:
        """获取工作器统计信息"""
        return self.stats.copy()


class RealTimeSyncManager:
    """实时同步管理器"""

    def __init__(self, user_manager: ThreadSafeUserManager):
        self.user_manager = user_manager
        self.current_users = set()
        self.user_queues = {}  # 每个worker一个队列
        self.queue_stats = {}  # 队列统计信息
        self.running = True

    def create_worker_queue(self, worker_id: int) -> Queue:
        """为worker创建专用队列"""
        queue = Queue()
        self.user_queues[worker_id] = queue
        self.queue_stats[worker_id] = {
            'queue_size': 0,
            'processed_count': 0,
            'avg_process_time': 9.0,  # 默认1秒处理时间
            'last_update': time.time()
        }
        return queue

    def update_queue_stats(self, worker_id: int, process_time: float):
        """更新队列统计信息"""
        if worker_id in self.queue_stats:
            stats = self.queue_stats[worker_id]
            stats['processed_count'] += 1
            # 使用指数移动平均计算平均处理时间
            alpha = 0.3  # 平滑因子
            stats['avg_process_time'] = (alpha * process_time +
                                       (1 - alpha) * stats['avg_process_time'])
            stats['last_update'] = time.time()

    def get_optimal_queue(self) -> Optional[int]:
        """获取最优队列（负载最轻的队列）"""
        if not self.user_queues:
            return None

        best_worker_id = None
        min_load_score = float('inf')

        for worker_id, queue in self.user_queues.items():
            stats = self.queue_stats[worker_id]
            current_size = queue.qsize()
            stats['queue_size'] = current_size

            # 计算负载分数：队列大小 * 平均处理时间
            load_score = current_size * stats['avg_process_time']

            if load_score < min_load_score:
                min_load_score = load_score
                best_worker_id = worker_id

        return best_worker_id

    def distribute_users(self):
        """智能分发用户到各个worker队列"""
        try:
            # 获取Redis中的当前用户
            redis_users = self.user_manager.get_redis_users()

            # 找出新增和删除的用户
            new_users = redis_users - self.current_users
            removed_users = self.current_users - redis_users

            # 处理删除的用户
            if removed_users:
                logger.info(f"检测到删除的用户: {len(removed_users)}")
                self.current_users -= removed_users

            # 智能分发新用户到负载最轻的队列
            if new_users:
                logger.info(f"检测到新用户: {len(new_users)}")
                for user_field in new_users:
                    optimal_worker_id = self.get_optimal_queue()
                    if optimal_worker_id is not None:
                        self.user_queues[optimal_worker_id].put(user_field)
                        logger.debug(f"用户 {user_field} 分配到 Worker-{optimal_worker_id}")

                self.current_users.update(new_users)

            # 重新分发需要处理的现有用户
            self.redistribute_existing_users()

            # 执行负载均衡
            self.rebalance_queues()

        except Exception as e:
            logger.error(f"用户分发异常: {e}")

    def redistribute_existing_users(self):
        """重新分发现有用户（检查状态变化）"""
        try:
            if not self.user_queues:
                return

            # 检查部分现有用户的状态
            users_to_check = list(self.current_users)[:] 

            for user_field in users_to_check:
                # 跳过正在处理的用户
                if self.user_manager.is_user_processing(user_field):
                    continue

                try:
                    user = hget_redis_user(user_field, spain_user_field)
                    if user and self._user_needs_processing(user):
                        # 智能选择最优队列
                        optimal_worker_id = self.get_optimal_queue()
                        if optimal_worker_id is not None:
                            try:
                                self.user_queues[optimal_worker_id].put_nowait(user_field)
                            except Exception:
                                pass  # 队列满时忽略
                except Exception as e:
                    logger.debug(f"检查用户状态异常 {user_field}: {e}")

        except Exception as e:
            logger.error(f"重新分发用户异常: {e}")

    def rebalance_queues(self):
        """负载均衡：将任务从繁忙队列转移到空闲队列"""
        try:
            if len(self.user_queues) < 2:
                return

            # 计算所有队列的负载
            queue_loads = []
            for worker_id, queue in self.user_queues.items():
                stats = self.queue_stats[worker_id]
                current_size = queue.qsize()
                load_score = current_size * stats['avg_process_time']
                queue_loads.append((worker_id, current_size, load_score))

            # 按负载排序
            queue_loads.sort(key=lambda x: x[2])  # 按load_score排序

            # 如果负载差异过大，进行重新平衡
            if len(queue_loads) >= 2:
                lightest = queue_loads[0]  # 负载最轻
                heaviest = queue_loads[-1]  # 负载最重

                # 如果最重队列的负载是最轻队列的3倍以上，进行重新平衡
                if heaviest[2] > lightest[2] * 3 and heaviest[1] > 5:
                    self._transfer_tasks(heaviest[0], lightest[0], min(3, heaviest[1] // 2))

        except Exception as e:
            logger.error(f"负载均衡异常: {e}")

    def _transfer_tasks(self, from_worker_id: int, to_worker_id: int, count: int):
        """在队列间转移任务"""
        try:
            from_queue = self.user_queues[from_worker_id]
            to_queue = self.user_queues[to_worker_id]

            transferred = 0
            for _ in range(count):
                try:
                    user_field = from_queue.get_nowait()
                    to_queue.put_nowait(user_field)
                    transferred += 1
                except Exception:
                    break

            if transferred > 0:
                logger.debug(f"负载均衡: Worker-{from_worker_id} -> Worker-{to_worker_id}, "
                           f"转移 {transferred} 个任务")

        except Exception as e:
            logger.error(f"任务转移异常: {e}")

    def _user_needs_processing(self, user: dict) -> bool:
        """检查用户是否需要处理"""
        if user.get("status") != "update_appointment":
            return False

        if get_user_date_status(user):
            return False

        if user.get("is_login", False):
            update_time = int(user.get("updateTime", 0))
            if int(time.time()) - update_time <= 600:
                return False

        return True

    def start_sync_loop(self):
        """启动同步循环"""
        logger.info("启动实时同步管理器")
        while self.running:
            try:
                self.distribute_users()
                time.sleep(1)  # 每秒同步一次
            except Exception as e:
                logger.error(f"同步循环异常: {e}")
                time.sleep(5)

    def stop(self):
        """停止同步"""
        self.running = False


class OptimizedLoginManager:
    """优化的登录管理器"""

    def __init__(self, worker_count: int = 15):
        self.worker_count = worker_count
        self.user_manager = ThreadSafeUserManager()
        self.sync_manager = RealTimeSyncManager(self.user_manager)
        self.workers = []
        self.worker_threads = []
        self.running = False

    def create_workers(self):
        """创建工作器"""
        for i in range(self.worker_count):
            worker = UserLoginWorker(i, self.user_manager, self.sync_manager)
            worker_queue = self.sync_manager.create_worker_queue(i)
            self.workers.append((worker, worker_queue))

    def worker_loop(self, worker: UserLoginWorker, queue: Queue):
        """工作器循环"""
        logger.info(f"Worker-{worker.worker_id} 启动")

        while self.running:
            try:
                # 从队列获取用户
                try:
                    user_field = queue.get(timeout=5)
                except Empty:
                    continue

                # 处理用户登录
                worker.login_user_safe(user_field)
                queue.task_done()

                # 短暂休息
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Worker-{worker.worker_id} 循环异常: {e}")
                time.sleep(1)

        logger.info(f"Worker-{worker.worker_id} 停止")

    def start(self):
        """启动登录管理器"""
        logger.info(f"启动优化登录管理器，工作器数量: {self.worker_count}")
        self.running = True

        # 创建工作器
        self.create_workers()

        # 启动工作器线程
        for worker, queue in self.workers:
            thread = threading.Thread(target=self.worker_loop, args=(worker, queue), daemon=True, name=f"LoginWorker-{worker.worker_id}")
            thread.start()
            self.worker_threads.append(thread)

        # 启动同步管理器
        sync_thread = threading.Thread(target=self.sync_manager.start_sync_loop, daemon=True, name="SyncManager")
        sync_thread.start()

        # 启动统计线程
        stats_thread = threading.Thread(target=self.stats_loop, daemon=True, name="StatsReporter")
        stats_thread.start()

    def stats_loop(self):
        """统计信息循环"""
        while self.running:
            try:
                time.sleep(1200)  # 每5分钟打印一次统计
                self.print_stats()
            except Exception as e:
                logger.error(f"统计循环异常: {e}")

    def print_stats(self):
        """打印统计信息"""
        total_stats = {"processed": 0, "success": 0, "failed": 0, "skipped": 0}

        for worker, _ in self.workers:
            stats = worker.get_stats()
            for key in total_stats:
                total_stats[key] += stats[key]

        if total_stats["processed"] > 0:
            success_rate = total_stats["success"] / total_stats["processed"] * 100
            logger.info(f"登录统计 - 处理: {total_stats['processed']}, "
                       f"成功: {total_stats['success']}, "
                       f"失败: {total_stats['failed']}, "
                       f"跳过: {total_stats['skipped']}, "
                       f"成功率: {success_rate:.1f}%")

        # 打印队列负载统计
        self.print_queue_stats()

    def print_queue_stats(self):
        """打印队列负载统计"""
        logger.info("队列负载统计:")
        queue_info = []

        for worker_id, queue in self.sync_manager.user_queues.items():
            stats = self.sync_manager.queue_stats[worker_id]
            current_size = queue.qsize()
            load_score = current_size * stats['avg_process_time']

            queue_info.append({
                'worker_id': worker_id,
                'queue_size': current_size,
                'processed': stats['processed_count'],
                'avg_time': stats['avg_process_time'],
                'load_score': load_score
            })

        # 按负载分数排序
        queue_info.sort(key=lambda x: x['load_score'], reverse=True)

        for info in queue_info:
            logger.info(f"  Worker-{info['worker_id']}: "
                       f"队列={info['queue_size']}, "
                       f"已处理={info['processed']}, "
                       f"平均耗时={info['avg_time']:.2f}s, "
                       f"负载分数={info['load_score']:.1f}")

        # 计算负载均衡度
        if queue_info:
            max_load = queue_info[0]['load_score']
            min_load = queue_info[-1]['load_score']
            if min_load > 0:
                balance_ratio = max_load / min_load
                logger.info(f"负载均衡度: {balance_ratio:.2f} (越接近1越均衡)")

    def stop(self):
        """停止管理器"""
        logger.info("停止登录管理器")
        self.running = False
        self.sync_manager.stop()

        # 等待线程结束
        for thread in self.worker_threads:
            thread.join(timeout=5)


def main():
    """主函数"""
    manager = OptimizedLoginManager(worker_count=15)

    try:
        manager.start()

        # 主循环
        while True:
            logger.info("登录服务正常运行中...")
            time.sleep(600)  # 每10分钟打印一次状态

    except KeyboardInterrupt:
        logger.info("收到停止信号")
    except Exception as e:
        logger.error(f"主循环异常: {e}")
    finally:
        manager.stop()


if __name__ == "__main__":
    main()
