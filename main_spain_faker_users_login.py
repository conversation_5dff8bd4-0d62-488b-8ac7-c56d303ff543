import sys
import time
from copy import deepcopy
from urllib3.util.retry import Retry
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import partial, reduce
from typing import List, Dict, Optional, Set, Callable
from itertools import filterfalse

from spain_visa_login import user_login
from extension.logger import logger
from extension.session_manager import create_session
from tool import get_new_proxy, get_random_user_agent
from config import headers, default_centers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, spain_faker_scaning, spain_user_field
from user_manager import get_user_centers, del_user_from_redis, get_user_info
from spain_visa_update_profile import update_user_profile, confirm_email

# 配置常量
DEFAULT_WORKER_COUNT = 20
LOGIN_TIMEOUT = 900  # 15分钟
BATCH_SIZE = 50
RETRY_DELAY = 2
PROCESS_DELAY = 1

# 设置重试策略
retries = Retry(
    total=3,
    backoff_factor=0.5,
    status_forcelist=[500, 502, 503, 504, 443],
)


# 纯函数：检查用户是否需要重新登录
def needs_relogin(user: Dict) -> bool:
    """检查用户是否需要重新登录"""
    if not user.get("is_login", False):
        return True

    time_now = int(time.time())
    update_time = int(user.get("updateTime", 0))
    return time_now - update_time > LOGIN_TIMEOUT

# 纯函数：准备用户会话头部
def prepare_session_header() -> Dict:
    """准备会话头部信息"""
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    return header

# 纯函数：获取用户VIP状态标识
def get_user_vip_label(user: Dict) -> str:
    """获取用户VIP状态标识"""
    return "VIP" if user.get("acceptVIP") == 1 else "Normal"

# 纯函数：更新用户登录状态
def update_user_login_state(user: Dict, success: bool, session=None, proxy: str = "") -> Dict:
    """更新用户登录状态"""
    user_copy = user.copy()
    user_copy["is_login"] = success
    user_copy["updateTime"] = int(time.time())

    if success and session:
        user_copy["cookies"] = session.cookies.get_dict()
        user_copy["proxy"] = proxy

    return user_copy

# 高阶函数：创建登录处理器
def create_login_processor() -> Callable[[Dict], Optional[Dict]]:
    """创建登录处理器"""

    def process_login(user: Dict) -> Optional[Dict]:
        """处理用户登录"""
        # 获取最新用户信息
        fresh_user = get_user_info(user)
        if not fresh_user:
            return None

        # 设置队列名称
        fresh_user["queue_name"] = spain_faker_scaning
        fresh_user.pop("headers", None)

        # 检查是否需要重新登录
        if not needs_relogin(fresh_user):
            return fresh_user

        # 获取代理
        proxy = get_new_proxy()
        if not proxy:
            return fresh_user

        # 创建会话
        header = prepare_session_header()
        session = create_session(proxy=proxy, header=header)

        # 执行登录
        vip_label = get_user_vip_label(fresh_user)
        logger.debug(f"##正在登录##{fresh_user.get('email')}, {fresh_user.get('centerCode')}, {vip_label}")

        flag_login, info_login = user_login(fresh_user, session)

        # 处理登录结果
        if not flag_login:
            if info_login == "deleted":
                del_user_from_redis(fresh_user)
                logger.error(f"##用户失效##{fresh_user.get('email')}, {fresh_user.get('centerCode')}, {vip_label}")
                return None

            logger.error(f"##用户登录失败##: {fresh_user.get('email')}, {fresh_user.get('centerCode')} {vip_label}")
            updated_user = update_user_login_state(fresh_user, False)
        else:
            logger.debug(f"##用户登录成功##: {fresh_user.get('email')}, {fresh_user.get('centerCode')} {vip_label}")
            updated_user = update_user_login_state(fresh_user, True, session, proxy)

        # 保存用户状态
        save_user_2_redis_queue(updated_user)

        if not flag_login:
            return updated_user

        # 处理用户状态流程
        return process_user_workflow(updated_user, session)

    return process_login

# 函数式处理用户工作流
def process_user_workflow(user: Dict, session) -> Dict:
    """处理用户工作流程"""
    # 更新用户信息
    if user["status"] == "login":
        user = process_profile_update(user, session)

    # 邮箱确认
    if user["status"] == "updated":
        user = process_email_confirmation(user, session)

    return user

def process_profile_update(user: Dict, session) -> Dict:
    """处理用户资料更新"""
    flag_profile, _ = update_user_profile(user, session)
    user_copy = user.copy()

    if flag_profile:
        logger.success(f"#注册# 更新成功:{user['email']} {user.get('visaTypeCode')}")
        user_copy["status"] = "updated"
    else:
        logger.error(f"#注册# 更新失败:{user['email']} {user.get('visaTypeCode')}")

    save_user_2_redis_queue(user_copy)
    return user_copy

def process_email_confirmation(user: Dict, session) -> Dict:
    """处理邮箱确认"""
    confirm_flag, _ = confirm_email(user, session)
    user_copy = user.copy()

    if confirm_flag:
        logger.success(f"#注册# 确认邮箱成功:{user['email']} {user.get('visaTypeCode')}")
        user_copy["status"] = "update_appointment"
    else:
        logger.error(f"#注册# 确认邮箱失败:{user['email']}")

    save_user_2_redis_queue(user_copy)
    return user_copy


# 函数式过滤器：过滤需要处理的用户
def filter_users_by_criteria(users: List[Dict], criteria: Callable[[Dict], bool]) -> List[Dict]:
    """根据条件过滤用户"""
    return list(filter(criteria, users))

# 函数式映射器：批量处理用户
def process_users_batch(users: List[Dict], processor: Callable[[Dict], Optional[Dict]]) -> List[Dict]:
    """批量处理用户"""
    return list(filter(None, map(processor, users)))

# 线程池处理器：并发处理用户
def process_users_concurrent(users: List[Dict], processor: Callable[[Dict], Optional[Dict]],
                           max_workers: int = DEFAULT_WORKER_COUNT) -> List[Dict]:
    """使用线程池并发处理用户"""
    if not users:
        return []

    results = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_user = {executor.submit(processor, user): user for user in users}

        # 收集结果
        for future in as_completed(future_to_user):
            try:
                result = future.result(timeout=30)  # 30秒超时
                if result:
                    results.append(result)
            except Exception as e:
                user = future_to_user[future]
                logger.error(f"处理用户 {user.get('email')} 时出错: {e}")

    return results

# 主要的扫描控制器
class UserLoginScanner:
    """用户登录扫描器"""

    def __init__(self, max_workers: int = DEFAULT_WORKER_COUNT):
        self.max_workers = max_workers
        self.login_processor = create_login_processor()
        self.processed_centers: Set[str] = set()

    def get_users_to_process(self) -> List[Dict]:
        """获取需要处理的用户"""
        all_faker_users = get_users_with_queue_name()

        # 如果是晚上8点后，处理所有用户
        if is_after_8pm():
            logger.warning("登录所有虚拟用户账号，保活")
            return all_faker_users

        return all_faker_users

    def process_user_batch(self, users: List[Dict]) -> List[Dict]:
        """处理用户批次"""
        if not users:
            return []

        logger.info(f"开始处理 {len(users)} 个用户")

        # 使用线程池并发处理
        processed_users = process_users_concurrent(
            users,
            self.login_processor,
            self.max_workers
        )

        logger.info(f"完成处理 {len(processed_users)} 个用户")
        return processed_users

    def run_continuous_scan(self):
        """运行持续扫描"""
        logger.info(f"#扫号#用户登录启动，工作线程数: {self.max_workers}")

        while True:
            try:
                # 获取需要处理的用户
                users_to_process = self.get_users_to_process()

                if not users_to_process:
                    logger.debug("没有用户需要处理，等待中...")
                    time.sleep(10)
                    continue

                # 分批处理用户
                for i in range(0, len(users_to_process), BATCH_SIZE):
                    batch = users_to_process[i:i + BATCH_SIZE]
                    self.process_user_batch(batch)

                    # 批次间延迟
                    if i + BATCH_SIZE < len(users_to_process):
                        time.sleep(PROCESS_DELAY)

                # 循环间延迟
                time.sleep(5)

            except Exception as e:
                logger.error(f"扫描过程中出错: {e}")
                time.sleep(RETRY_DELAY)

# 便捷函数：启动扫描
def start_scanning(thread_count: int = DEFAULT_WORKER_COUNT):
    """启动用户登录扫描"""
    scanner = UserLoginScanner(max_workers=thread_count)
    scanner.run_continuous_scan()

def is_after_8pm():
    """
    判断当前时间是否是晚上八点之后（包括 20:00:00）。

    返回:
        bool: 如果当前时间是晚上八点之后，返回 True；否则返回 False。
    """
    # 获取当前时间
    now = datetime.now()

    # 获取当前小时和分钟
    current_hour = now.hour
    current_minute = now.minute

    # 判断是否是晚上八点之后
    if current_hour > 20 or (current_hour == 20 and current_minute >= 30):
        return True
    else:
        return False


if __name__ == "__main__":
    # 获取外部参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if len(args) > 0 else DEFAULT_WORKER_COUNT
    start_scanning(thread_count)

