#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版西班牙签证用户注册和信息修改模块
主要优化点：
1. 多线程处理 - 线程之间独立运行，避免相互阻塞
2. 实时用户更新 - 及时获取和更新需要处理的用户状态
3. 简化架构 - 减少复杂的类封装，使用函数式编程
4. 资源管理 - 优化代理和会话管理
5. 错误处理 - 完善的重试和异常处理机制
"""

import time
import sys
import threading
from queue import Queue, Empty
from copy import deepcopy
from typing import Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from urllib3.util.retry import Retry

# 导入现有模块
from spain_visa_registe import user_registe
from spain_visa_login import user_login
from spain_visa_update_profile import update_user_profile, confirm_email
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent, generate_phone_number, generate_random_email, send_dd_msg, get_user_log_msg, send_wx_msg, update_booking_status, BOOK_STATUS, get_user_head_img
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, spain_user_field, get_user_info

# 配置常量
DEFAULT_THREAD_COUNT = 4
SLEEP_INTERVAL = 5
MAX_RETRY_COUNT = 2
SESSION_TIMEOUT = 15

# 设置重试策略
RETRY_STRATEGY = Retry(
    total=5,
    backoff_factor=0.5,
    status_forcelist=[500, 502, 503, 504, 443],
)

# 必填字段列表
REQUIRED_FIELDS = ["birthday", "gender", "maritalStatus", "travelDate", "visaTypeCode", "expiredDT", "passportNO", "passportDate", "signLocation", "name", "xing", "startDate", "endDate", "bornplace", "centerCode"]


def validate_user_data(user: Dict) -> List[str]:
    """验证用户数据完整性"""
    missing_fields = []
    for field in REQUIRED_FIELDS:
        if not user.get(field):
            missing_fields.append(field)
    return missing_fields


def create_session_with_proxy() -> Optional[requests.Session]:
    """创建带代理的会话"""
    try:
        session = requests.Session()
        header = deepcopy(headers)
        header["user-agent"] = get_random_user_agent()
        session.headers = header

        proxy = get_new_proxy()
        if not proxy:
            logger.error("无法获取代理")
            return None

        proxy_dict = {"http": proxy, "https": proxy}
        session.proxies.update(proxy_dict)
        session.proxy_url = proxy  # 保存代理信息用于后续更新

        return session
    except Exception as e:
        logger.error(f"创建会话失败: {e}")
        return None


def update_session_proxy(session: requests.Session) -> bool:
    """更新会话代理"""
    try:
        proxy = get_new_proxy()
        if not proxy:
            return False
        proxy_dict = {"http": proxy, "https": proxy}
        session.proxies.update(proxy_dict)
        session.proxy_url = proxy
        return True
    except Exception as e:
        logger.error(f"更新代理失败: {e}")
        return False


def upload_user_avatar(user: Dict, session: requests.Session) -> tuple:
    """上传用户头像"""
    try:
        session.headers.update(
            {
                "requestverificationtoken": "CfDJ8ExjRtuoEK1GjHvsUsGg4ZxrZIiOVePGscdiW18F16aSWboB3-Cf4beYAViMgaNyWYVuaQsHlTZ4fiBmcKh8x5SeBQ_cKbX5WEKzFPLWGqSZtjhbc_vfqhIi6WHEQFZMAGdQCY16YsrRyv4FfrHdv1FaFdclBL4PhDfXHRX-nCC8uWFv20Cj850GF4Ty5fM0Yw",
                "x-requested-with": "XMLHttpRequest",
            }
        )

        avatar_images = user.get("avatar_images") or [user.get("avatar_image")]
        url = "https://spain.blscn.cn/CHN/appointment/UploadApplicantPhoto"

        for avatar_image in avatar_images:
            if not avatar_image:
                continue

            try:
                img_bytes = get_user_head_img(user, avatar_image)
                response = session.post(url, files={"file": (f"{user['passportNO']}.png", img_bytes, "image/jpeg")}, timeout=SESSION_TIMEOUT)

                if response.status_code == 200 and response.json().get("success"):
                    return True, response.json()
                else:
                    return False, response.json() if response.status_code == 200 else response.status_code

            except Exception as e:
                logger.error(f"头像上传重试: {e}")
                if not update_session_proxy(session):
                    break

        return False, "头像上传失败"
    except Exception as e:
        logger.error(f"头像上传异常: {e}")
        return False, str(e)


def process_user_registration(user: Dict) -> bool:
    """处理用户注册流程"""
    user_id = f"{user.get('chnname')}-{user.get('passportNO')}"

    # 验证用户数据
    missing_fields = validate_user_data(user)
    if missing_fields:
        error_msg = f"用户{user_id}注册缺少必填字段: {' | '.join(missing_fields)}"
        send_dd_msg(f"#西班牙注册错误# {error_msg}")
        send_wx_msg(f"#西班牙注册错误# {error_msg}", user.get("passportNO"))
        return False

    # 创建会话
    session = create_session_with_proxy()
    if not session:
        logger.error(f"用户{user_id}创建会话失败")
        return False

    try:
        # 处理注册逻辑
        if user.get("status") == "pending":
            success = handle_user_registration(user, session)
            if not success:
                return False

        # 处理登录逻辑
        login_success = handle_user_login(user, session)
        if not login_success:
            return False

        # 处理信息更新逻辑
        if user["status"] in ["login", "update_info"]:
            update_success = handle_profile_update(user, session)
            if not update_success:
                return False

        # 处理邮箱确认逻辑
        if user["status"] == "updated":
            confirm_success = handle_email_confirmation(user, session)
            return confirm_success

        return True

    except Exception as e:
        logger.error(f"处理用户{user_id}时发生异常: {e}")
        return False
    finally:
        session.close()


def handle_user_registration(user: Dict, session: requests.Session) -> bool:
    """处理用户注册"""
    user["email"] = generate_random_email()
    user["phone"] = generate_phone_number()
    user["centerCode"] = user.get("centerCode").upper()
    user["queue_name"] = spain_user_field
    user.pop("password", None)

    user_id = f"{user.get('chnname')}-{user.get('passportNO')}"
    logger.info(f"开始注册用户: {user_id} email:{user['email']}, area:{user.get('centerCode')}")

    flag_register, info = user_registe(user, session)
    if not flag_register:
        logger.error(f"注册失败: {user_id}, 错误信息: {info}")
        return False

    try:
        log_msg = get_user_log_msg(user)
        send_dd_msg(f"#西班牙注册用户# {log_msg}")
    except Exception as e:
        logger.warning(f"发送注册成功消息失败: {e}")

    logger.success(f"注册成功: {user_id}, 日期:{user.get('startDate')}-{user.get('endDate')}")
    user["status"] = "registe"
    user["proxy"] = session.proxy_url

    # 检查用户是否仍在队列中
    if get_user_info(user):
        save_user_2_redis_queue(user)
        update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "注册成功")
    else:
        logger.info(f"注册成功但用户已被删除: {user_id}")

    return True


def handle_user_login(user: Dict, session: requests.Session) -> bool:
    """处理用户登录"""
    user_id = f"{user.get('chnname')}-{user.get('passportNO')}"

    for attempt in range(MAX_RETRY_COUNT):
        flag_login, info_login = user_login(user, session)
        if flag_login:
            logger.success(f"登录成功: {user_id}")

            # 更新用户信息
            user["cookies"] = session.cookies.get_dict()
            user["proxy"] = session.proxy_url
            if user["status"] != "update_info":
                user["status"] = "login"
            user["is_login"] = True
            user["updateTime"] = int(time.time())
            save_user_2_redis_queue(user)
            return True
        else:
            logger.error(f"登录失败 (尝试 {attempt + 1}/{MAX_RETRY_COUNT}): {user_id}, 错误信息: {info_login}")
            if attempt < MAX_RETRY_COUNT - 1:
                if not update_session_proxy(session):
                    break

    return False


def handle_profile_update(user: Dict, session: requests.Session) -> bool:
    """处理用户信息更新"""
    user_id = f"{user.get('chnname')}-{user.get('passportNO')}"

    flag_profile, res_profile = update_user_profile(user, session)
    if not flag_profile:
        logger.error(f"更新失败: {user_id}, 错误信息: {res_profile}")
        return False

    logger.success(f"更新成功: {user_id}")

    if user["status"] == "login":
        user["status"] = "updated"
    elif user["status"] == "update_info":
        user["status"] = "update_appointment"
        log_msg = get_user_log_msg(user)
        # 处理头像上传
        head_msg = "照片可用"
        if not user.get("image_id"):
            try:
                head_upload, res_upload = upload_user_avatar(user, session)
                if head_upload:
                    user["image_id"] = res_upload.get("fileId")
                    head_msg = "照片可用:" + str(res_upload)
                    update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "更新信息成功，头像可用")
                else:
                    update_booking_status(user, BOOK_STATUS.AVATAR_FORBIDDEN, "更新信息成功，头像不可用")
                    head_msg = "照片不可用:" + str(res_upload)
            except Exception as e:
                logger.error(f"{log_msg}: {e}")
        else:
            update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "更新信息成功，头像可用")

        send_dd_msg(f"#西班牙用户修改信息成功# {log_msg} {head_msg}")

    save_user_2_redis_queue(user)
    return True


def handle_email_confirmation(user: Dict, session: requests.Session) -> bool:
    """处理邮箱确认"""
    user_id = f"{user.get('chnname')}-{user.get('passportNO')}"

    confirm_flag, res_confirm = confirm_email(user, session)
    if not confirm_flag:
        logger.error(f"确认邮箱失败: {user['email']}, 用户: {user_id}, 错误信息: {res_confirm}")
        return False

    logger.success(f"确认邮箱成功: {user['email']}")
    user["status"] = "update_appointment"

    # 上传头像
    head_upload, res_upload = upload_user_avatar(user, session)
    head_msg = "照片可用:" + str(res_upload) if head_upload else "照片不可用:" + str(res_upload)

    if head_upload:
        user["image_id"] = res_upload.get("fileId")

    save_user_2_redis_queue(user)

    try:
        log_msg = get_user_log_msg(user)
        send_dd_msg(f"#西班牙填表成功# {log_msg}, {head_msg}")

        if head_upload:
            update_booking_status(user, BOOK_STATUS.REGISTE_SUCCESS, "填表成功，头像可用")
        else:
            update_booking_status(user, BOOK_STATUS.AVATAR_FORBIDDEN, "填表成功，头像不可用")
    except Exception as e:
        logger.warning(f"发送邮箱确认成功消息失败: {e}")

    return True


def worker_thread(user_queue: Queue):
    """工作线程函数"""
    while True:
        try:
            user = user_queue.get(timeout=1)
            if user is None:  # 结束信号
                break
            process_user_registration(user)
            user_queue.task_done()
        except Empty:
            continue
        except Exception as e:
            logger.error(f"工作线程异常: {e}")


def get_pending_users() -> List[Dict]:
    """获取待处理用户列表"""
    try:
        all_users = get_users_with_queue_name(spain_user_field)
        return [user for user in all_users if user.get("status") in ["pending", "registe", "update_info", "login", "updated"]]
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        return []


def main_registration_pipeline(thread_count: int = DEFAULT_THREAD_COUNT):
    """主注册流程管道 - 使用线程池优化版本"""
    logger.info(f"启动西班牙用户注册服务，线程数: {thread_count}")

    # 使用线程池管理，避免频繁创建销毁线程
    with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="RegWorker") as executor:
        while True:
            try:
                work_start = time.time()

                # 获取待处理用户
                pending_users = get_pending_users()

                if not pending_users:
                    logger.debug(f"队列无用户: {spain_user_field}")
                    time.sleep(SLEEP_INTERVAL)
                    continue

                # 为每个用户添加队列名
                for user in pending_users:
                    user["queue_name"] = spain_user_field

                # 提交任务到线程池，每个任务独立处理
                futures = [executor.submit(process_user_registration, user) for user in pending_users]

                # 等待所有任务完成，统计结果
                completed_count = 0
                failed_count = 0

                for future in as_completed(futures):
                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        if result:
                            completed_count += 1
                        else:
                            failed_count += 1
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"任务执行异常: {e}")

                elapsed_time = int(time.time() - work_start)
                logger.info(f"#西班牙用户注册# 完成{completed_count}个，失败{failed_count}个，总计{len(pending_users)}个用户，耗时: {elapsed_time}s")

                # 动态调整休眠时间
                if completed_count == 0 and len(pending_users) > 0:
                    time.sleep(SLEEP_INTERVAL * 2)  # 如果全部失败，延长休眠时间
                else:
                    time.sleep(SLEEP_INTERVAL)

            except KeyboardInterrupt:
                logger.info("收到中断信号，正在关闭...")
                break
            except Exception as e:
                logger.error(f"主流程异常: {e}")
                time.sleep(SLEEP_INTERVAL)


def alternative_queue_based_pipeline(thread_count: int = DEFAULT_THREAD_COUNT):
    """备选方案：基于队列的多线程处理"""
    logger.info(f"启动基于队列的西班牙用户注册服务，线程数: {thread_count}")

    user_queue = Queue(maxsize=100)  # 限制队列大小避免内存溢出

    # 创建工作线程
    threads = []
    for i in range(thread_count):
        thread = threading.Thread(target=worker_thread, args=(user_queue,), name=f"RegWorker-{i}", daemon=True)
        thread.start()
        threads.append(thread)

    try:
        while True:
            work_start = time.time()

            # 获取待处理用户
            pending_users = get_pending_users()

            if not pending_users:
                logger.debug(f"队列无用户: {spain_user_field}")
                time.sleep(SLEEP_INTERVAL)
                continue

            # 将用户添加到队列
            for user in pending_users:
                user["queue_name"] = spain_user_field
                user_queue.put(user)

            # 等待队列处理完成
            user_queue.join()

            elapsed_time = int(time.time() - work_start)
            logger.info(f"#西班牙用户注册# 处理{len(pending_users)}个用户，耗时: {elapsed_time}s")

            time.sleep(SLEEP_INTERVAL)

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
        # 发送结束信号给所有工作线程
        for _ in range(thread_count):
            user_queue.put(None)
        # 等待所有线程结束
        for thread in threads:
            thread.join(timeout=5)
    except Exception as e:
        logger.error(f"队列处理流程异常: {e}")


def get_user_processing_stats() -> Dict:
    """获取用户处理统计信息"""
    try:
        all_users = get_users_with_queue_name(spain_user_field)
        stats = {"total": len(all_users), "pending": 0, "registe": 0, "login": 0, "updated": 0, "update_info": 0, "update_appointment": 0, "other": 0}

        for user in all_users:
            status = user.get("status", "unknown")
            if status in stats:
                stats[status] += 1
            else:
                stats["other"] += 1

        return stats
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return {}


if __name__ == "__main__":
    # 获取命令行参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if args and args[0].isdigit() else DEFAULT_THREAD_COUNT

    # 支持选择不同的处理模式
    mode = args[1] if len(args) > 1 else "threadpool"

    logger.info(f"开始监控新用户并注册，线程数: {thread_count}, 模式: {mode}")

    # 显示初始统计信息
    initial_stats = get_user_processing_stats()
    if initial_stats:
        logger.info(f"初始用户状态统计: {initial_stats}")

    try:
        if mode == "queue":
            alternative_queue_based_pipeline(thread_count)
        else:
            main_registration_pipeline(thread_count)
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
