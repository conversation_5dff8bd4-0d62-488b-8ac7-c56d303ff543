#!/bin/bash

/bin/bash /root/visa/SpainVisa/stop_spain_app.sh

cd /root/visa/SpainVisa
source /root/visa/conda_env/bin/activate

echo "启动脚本 main_spain_users_login_v2.py"
nohup python3 main_spain_users_login_v2.py > logs_today/spain_users_login.log 2>&1 &

echo "启动脚本 main_spain_users_date_appointment.py"
# nohup python3 main_spain_users_date_appointment.py 15 > logs_today/spain_users_appointment.log 2>&1 &
nohup python3 main_spain_users_date_app_optimeized.py > logs_today/spain_users_date_optmeized.log 2>&1 &

echo "启动脚本 main_spain_users_registe.py"
# nohup python3 main_spain_users_registe.py 2 > logs_today/spain_users_registe.log 2>&1 &
nohup python3 main_spain_users_registe_optimized.py 5 > logs_today/spain_users_registe.log 2>&1 &

echo "启动脚本 main_spain_notify_pay.py"
nohup python3 main_spain_notify_pay.py 2 > logs_today/spain_notity_pay.log 2>&1 &

echo "启动脚本 main_spain_account_delete.py"
nohup python3 main_spain_account_delete.py 2 > logs_today/delete_users.log 2>&1 &
# nohup python3 api.py > api.log 2>&1 &

