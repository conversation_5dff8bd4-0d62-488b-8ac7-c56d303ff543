"""
西班牙签证预约系统 - 多通知优化版本

核心功能：
1. 支持多个Redis通知并发处理，互不干扰
2. 每次通知进行多轮预约，提高成功率
3. 智能去重机制，避免重复处理
4. 线程池管理，高效资源利用
"""

import requests
import sys
import time
import threading
import json
import uuid
from copy import deepcopy
from typing import Dict, List
from concurrent.futures import ThreadPoolExecutor

from spain_visa_date_appointment import date_appointment
from extension.logger import logger
from tool import get_random_user_agent, str_2_timestamp
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, subscribe_redis_msg, get_user_info
from user_manager import get_user_date_status, set_user_date_status, spain_user_field

# 全局配置
REDIS_FIELD_PREFIX = "spain-"  # Redis字段前缀
MONITOR_INTERVAL = 2  # 监控间隔（秒）
WORKER_THREAD_COUNT = 50  # 每个地区的工作线程数
MAX_RETRY_COUNT = 3  # 最大重试次数
ROUND_INTERVAL = 5  # 每轮预约之间的间隔（秒）
REDIS_CHECK_INTERVAL = 5  # Redis值检查间隔（秒）

# 全局管理器
notification_managers: Dict[str, "NotificationAppointmentManager"] = {}
redis_subscriber = None
main_executor = ThreadPoolExecutor(max_workers=160, thread_name_prefix="NotificationHandler")


class RedisSubscriber:
    """Redis订阅器，处理多个Redis通知"""

    def __init__(self):
        self.running = False
        self.subscriber_thread = None
        self.processed_notifications = {}  # 存储已处理的通知 {notification_hash: timestamp}
        self.active_notifications = {}  # 存储正在处理的通知 {notification_key: manager}

    def start_subscribing(self):
        """开始订阅Redis消息"""
        if self.running:
            return

        self.running = True
        self.subscriber_thread = threading.Thread(target=self._subscribe_loop, daemon=True)
        self.subscriber_thread.start()
        logger.info("Redis订阅器已启动")

    def stop_subscribing(self):
        """停止订阅"""
        self.running = False
        if self.subscriber_thread:
            self.subscriber_thread.join()
        logger.info("Redis订阅器已停止")

    def _subscribe_loop(self):
        """订阅循环"""
        try:
            subscribe_redis_msg(self._handle_redis_message)
            while self.running:
                time.sleep(1)
        except Exception as e:
            logger.error(f"Redis订阅异常: {e}")

    def _handle_redis_message(self, channel, msg_str):
        """处理Redis消息"""
        try:
            # 生成通知的唯一标识
            notification_hash = self._generate_notification_hash(msg_str)
            current_time = time.time()

            # 检查是否为重复通知
            if self._is_duplicate_notification(notification_hash, current_time):
                logger.debug(f"忽略重复通知: {msg_str}")
                return

            # 清理过期的通知记录
            self._cleanup_expired_notifications(current_time)

            # 为每个Redis通知创建独立的处理器
            notification_id = notification_hash  # str(uuid.uuid4())
            logger.info(f"收到Redis通知 {notification_id} (hash: {notification_hash}): {msg_str}")

            # 记录通知
            self.processed_notifications[notification_hash] = current_time

            # 使用线程池异步处理通知，确保不同通知之间不相互影响
            main_executor.submit(self._process_notification, notification_id, msg_str, notification_hash)

        except Exception as e:
            logger.error(f"处理Redis消息异常: {e}")

    def _generate_notification_hash(self, msg_str: str) -> str:
        """生成通知的唯一标识 - 基于centerCode、visaType、isVIP"""
        try:
            msg_json = json.loads(msg_str)
            center_code = msg_json.get("centerCode", "")
            visa_type = msg_json.get("visaType", "")
            is_vip = msg_json.get("isVIP", False)

            # 生成与Redis键匹配的标识
            notification_key = f"spain-{center_code}-{visa_type}-{is_vip}"
            return notification_key
        except (json.JSONDecodeError, KeyError, TypeError):
            # 如果解析失败，返回默认值
            return "unknown-unknown-False"

    def _is_duplicate_notification(self, notification_hash: str, current_time: float) -> bool:
        """检查是否为重复通知 - 基于Redis值是否存在"""
        from user_manager import redis_client

        # 检查Redis中是否存在对应的值
        # redis_key = f"spain-{notification_hash}"
        redis_value = redis_client.get(notification_hash)

        if not redis_value:
            logger.debug(f"Redis中不存在 {notification_hash}，忽略通知")
            return True  # Redis中没有值，认为是重复/无效通知

        # 检查是否有相同的通知正在处理
        if notification_hash in self.active_notifications:
            manager = self.active_notifications[notification_hash]
            if manager.running:
                logger.debug(f"通知 {notification_hash} 正在处理中，忽略重复通知")
                return True

        return False

    def _cleanup_expired_notifications(self, current_time: float):
        """清理已完成的活跃通知"""
        # 创建副本以避免在迭代时字典大小变化
        active_copy = dict(self.active_notifications)
        completed_keys = []

        for key, manager in active_copy.items():
            if not manager.running:
                completed_keys.append(key)

        for key in completed_keys:
            if key in self.active_notifications:
                del self.active_notifications[key]

    def _process_notification(self, notification_id: str, msg_str: str, notification_hash: str):
        """处理单个通知"""
        try:
            msg_json = json.loads(msg_str)
            center_code = msg_json.get("centerCode")
            visa_type = msg_json.get("visaType")

            if not center_code or not visa_type:
                logger.warning(f"通知 {notification_id} 缺少必要信息: {msg_json}")
                return

            # 创建通知管理器
            manager = NotificationAppointmentManager(notification_id, msg_json)
            notification_managers[notification_id] = manager

            # 将管理器添加到活跃通知中
            self.active_notifications[notification_hash] = manager

            # 启动多轮预约
            manager.start_multi_round_appointments()

        except json.JSONDecodeError:
            logger.error(f"通知 {notification_id} JSON解析失败: {msg_str}")
        except Exception as e:
            logger.error(f"处理通知 {notification_id} 异常: {e}")


class NotificationAppointmentManager:
    """通知预约管理器，负责处理单个Redis通知的多轮预约"""

    def __init__(self, notification_id: str, appointment_info: dict):
        self.notification_id = notification_id
        self.appointment_info = appointment_info
        self.center_code = appointment_info.get("centerCode")
        self.visa_type = appointment_info.get("visaType")
        self.is_vip = appointment_info.get("isVIP", False)
        self.open_days = appointment_info.get("dates", [])

        self.running = False
        self.round_executor = ThreadPoolExecutor(max_workers=WORKER_THREAD_COUNT if self.center_code == "SHANGHAI" else 20, thread_name_prefix=f"Round-{self.notification_id}")

        self.stats = {"total_rounds": 0, "processed_users": 0, "successful_appointments": 0, "failed_appointments": 0, "start_time": time.time()}

    def start_multi_round_appointments(self):
        """启动多轮预约"""
        if self.running:
            return

        self.running = True
        logger.info(f"#预约# 开始多轮预约: {self.center_code}-{self.visa_type}-VIP:{self.is_vip}")

        # 在独立线程中执行多轮预约
        threading.Thread(target=self._execute_multi_rounds, daemon=True, name=f"MultiRound-{self.notification_id}").start()

    def _execute_multi_rounds(self):
        """执行多轮预约 - 基于Redis值循环"""
        from user_manager import redis_client

        try:
            round_num = 0
            # 使用通知的关键信息构建Redis键
            center_code = self.appointment_info.get("centerCode", "")
            visa_type = self.appointment_info.get("visaType", "")
            is_vip = self.appointment_info.get("isVIP", False)
            redis_key = f"spain-{center_code}-{visa_type}-{is_vip}"

            logger.info(f"通知 {self.notification_id} 开始预约，Redis键: {redis_key}")
            while self.running:
                # 检查Redis中是否还有值
                redis_value = redis_client.get(redis_key)
                if not redis_value:
                    logger.info(f"通知 {self.notification_id} Redis键 {redis_key} 值为空，停止预约")
                    break

                # 需要从redisvalue更新放号日期信息
                appointment_info = json.loads(redis_value)
                self.appointment_info = appointment_info
                self.center_code = appointment_info.get("centerCode")
                self.visa_type = appointment_info.get("visaType")
                self.is_vip = appointment_info.get("isVIP", False)
                self.open_days = appointment_info.get("dates", [])

                round_num += 1
                # logger.info(f"通知 {self.notification_id} 开始第 {round_num} 轮预约，Redis键: {redis_key}")
                self.stats["total_rounds"] = round_num

                # 执行单轮预约
                self._execute_single_round(round_num)

                # 轮次间隔
                time.sleep(REDIS_CHECK_INTERVAL)

        except Exception as e:
            logger.error(f"通知 {self.notification_id} 多轮预约异常: {e}")
        finally:
            self._cleanup()

    def _execute_single_round(self, round_num: int):
        """执行单轮预约"""
        try:
            # 获取符合条件的用户
            eligible_users = self._get_eligible_users()

            if not eligible_users:
                logger.debug(f"通知 {self.notification_id} 第 {round_num} 轮无符合条件的用户")
                return

            logger.info(f"通知 {self.notification_id} 第 {round_num} 轮找到 {len(eligible_users)} 个符合条件的用户")

            # 使用线程池并发处理用户预约
            futures = []
            for user in eligible_users:
                future = self.round_executor.submit(self._process_user_appointment, user, round_num)
                futures.append(future)

            # 等待所有预约完成
            for future in futures:
                try:
                    future.result(timeout=300)  # 300秒超时
                except Exception as e:
                    logger.error(f"通知 {self.notification_id} 第 {round_num} 轮用户预约异常: {e}")

        except Exception as e:
            logger.error(f"通知 {self.notification_id} 第 {round_num} 轮预约异常: {e}")

    def _get_eligible_users(self) -> List[dict]:
        """获取符合条件的用户"""
        try:
            # 获取所有用户
            all_users = get_users_with_queue_name(spain_user_field)

            # 筛选等待预约的用户
            waiting_users = list(filter(lambda u: (u["status"] == "update_appointment" and u.get("centerCode") == self.center_code and u.get("visaTypeCode") == self.visa_type), all_users))

            # VIP筛选
            if self.is_vip:
                waiting_users = list(filter(lambda u: u["acceptVIP"] == 1, waiting_users))

            # 筛选期望日期匹配的用户
            eligible_users = []
            for user in waiting_users:
                if self._date_available(user, self.open_days):
                    user["dateVIP"] = self.is_vip
                    eligible_users.append(user)

            return eligible_users

        except Exception as e:
            logger.error(f"通知 {self.notification_id} 获取符合条件用户异常: {e}")
            return []

    def _process_user_appointment(self, user: dict, round_num: int):
        """处理单个用户的预约"""
        try:
            # 获取最新用户信息
            fresh_user = get_user_info(user)
            if not fresh_user:
                return

            # 保持VIP标记
            fresh_user["dateVIP"] = user.get("dateVIP", False)

            # 检查用户预约状态，避免重复预约
            if get_user_date_status(fresh_user):
                logger.debug(f"通知 {self.notification_id} 第 {round_num} 轮用户 {fresh_user.get('email')} 正在预约中，跳过")
                return

            # 设置预约中状态
            set_user_date_status(fresh_user, True)
            self.stats["processed_users"] += 1

            try:
                # 执行预约
                flag_book, _ = date_job(fresh_user)

                if flag_book:
                    self.stats["successful_appointments"] += 1
                    logger.success(f"通知 {self.notification_id} 第 {round_num} 轮预约成功: {fresh_user.get('email')}")
                else:
                    self.stats["failed_appointments"] += 1
                    # logger.info(f"通知 {self.notification_id} 第 {round_num} 轮预约失败: {fresh_user.get('email')}")

            finally:
                # 重置预约状态
                set_user_date_status(fresh_user, False)

        except Exception as e:
            logger.error(f"通知 {self.notification_id} 第 {round_num} 轮处理用户预约异常: {e}")

    def _date_available(self, user_info: dict, all_dates: List[str]) -> bool:
        """检查用户期望日期是否可用"""
        if len(all_dates) == 0:
            return True

        start_date = user_info.get("startDate", "").replace("/", "-")
        end_date = user_info.get("endDate", "").replace("/", "-")

        # 判断是否有可预约日期
        for date_item in all_dates:
            try:
                if str_2_timestamp(start_date) <= str_2_timestamp(date_item) <= str_2_timestamp(end_date):
                    return True
            except Exception:
                continue
        return False

    def _cleanup(self):
        """清理资源"""
        try:
            self.running = False
            self.round_executor.shutdown(wait=True)

            # 从全局管理器中移除
            if self.notification_id in notification_managers:
                del notification_managers[self.notification_id]

            # 输出统计信息
            duration = time.time() - self.stats["start_time"]
            logger.info(
                f"通知 {self.notification_id} 预约完成: "
                f"轮数={self.stats['total_rounds']}, "
                f"处理用户={self.stats['processed_users']}, "
                f"成功预约={self.stats['successful_appointments']}, "
                f"失败预约={self.stats['failed_appointments']}, "
                f"耗时={duration:.2f}秒"
            )

        except Exception as e:
            logger.error(f"通知 {self.notification_id} 清理资源异常: {e}")

    def get_stats(self) -> dict:
        """获取统计信息"""
        stats = self.stats.copy()
        stats["running"] = self.running
        return stats


def date_job(user):
    """执行用户预约"""
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()

    if not user.get("is_login", False):
        logger.warning(f"##预约## 用户未登录:{user.get('chnname')}-{user.get('email')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}")
        return False, None

    logger.info(f"##开始预约##: {user.get('chnname')}-{user.get('email')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}")

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    proxy_dict = {"http": proxy, "https": proxy}
    session = requests.session()
    session.proxies.update(proxy_dict)
    session.cookies.update(cookie_dict)
    session.headers.update(header)

    # 执行预约
    flag_book, done_job = date_appointment(user, session)
    if not flag_book:
        user["is_login"] = False
        save_user_2_redis_queue(user)
    return flag_book, done_job


def start_appointment_system():
    """启动预约系统"""
    global redis_subscriber

    logger.info("启动多通知预约系统")

    # 重置用户预约状态
    all_users = get_users_with_queue_name(spain_user_field)
    for user in all_users:
        set_user_date_status(user, False)

    # 启动Redis订阅器
    redis_subscriber = RedisSubscriber()
    redis_subscriber.start_subscribing()

    logger.info("系统启动完成")

    # 保持主线程运行
    try:
        while True:
            time.sleep(600)
            _print_system_stats()
    except KeyboardInterrupt:
        logger.info("正在关闭系统...")
        stop_appointment_system()


def stop_appointment_system():
    """停止预约系统"""
    global redis_subscriber, notification_managers, main_executor

    logger.info("正在停止系统...")

    if redis_subscriber:
        redis_subscriber.stop_subscribing()

    # 停止所有通知管理器 - 使用副本避免迭代时字典变化
    managers_copy = list(notification_managers.values())
    for manager in managers_copy:
        manager.running = False

    main_executor.shutdown(wait=True, timeout=30)
    notification_managers.clear()
    logger.info("系统已停止")


def _print_system_stats():
    """打印系统统计信息"""
    # 创建字典的副本以避免在迭代时字典大小变化的问题
    managers_copy = dict(notification_managers)

    if not managers_copy:
        return

    for notification_id, manager in managers_copy.items():
        try:
            stats = manager.get_stats()
            logger.info(f"##系统统计## 通知 {notification_id[:]}: 轮数={stats['total_rounds']}, " f"处理={stats['processed_users']}, " f"成功={stats['successful_appointments']}, " f"失败={stats['failed_appointments']}")
        except Exception as e:
            logger.debug(f"##系统统计## 获取通知 {notification_id[:]} 统计信息失败: {e}")


def test_single_region(center_code="GUANGZHOU"):
    """测试单个地区"""
    test_notification = {
        "centerCode": center_code,
        "dates": ["2025-07-25", "2025-07-26", "2025-07-27"],
        "isVIP": False,
        "visaType": "Tourism",
    }

    logger.info(f"测试地区: {center_code}")

    # 创建通知管理器直接处理
    notification_id = str(uuid.uuid4())
    manager = NotificationAppointmentManager(notification_id, test_notification)
    notification_managers[notification_id] = manager
    manager.start_multi_round_appointments()

    # 等待处理完成
    while manager.running:
        time.sleep(1)

    # 显示结果
    stats = manager.get_stats()
    logger.info(f"测试完成: {stats}")


# nohup python3 main_spain_users_date_app_optimeized.py > logs_today/spain_users_date_optmeized.log 2>&1 &
if __name__ == "__main__":
    args = sys.argv[1:]

    if len(args) == 0 or args[0] in ["multi", "start"]:
        # 启动预约系统
        start_appointment_system()
    elif args[0] == "test":
        # 测试模式
        center_code = args[1] if len(args) > 1 else "GUANGZHOU"
        test_single_region(center_code)
    else:
        logger.error(f"未知参数: {args[0]}")
        logger.info("使用方法:")
        logger.info("  python main_spain_users_date_app_optimeized.py        # 启动预约系统")
        logger.info("  python main_spain_users_date_app_optimeized.py start  # 启动预约系统")
        logger.info("  python main_spain_users_date_app_optimeized.py test [地区] # 测试模式")
