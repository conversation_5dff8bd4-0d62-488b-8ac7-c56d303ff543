import requests
import sys
import time
import threading
import json
from queue import Queue
from copy import deepcopy
from datetime import datetime

# from spain_visa_appointment_date_open import book_appointment
from spain_visa_date_appointment import date_appointment
from extension.logger import logger
from tool import get_random_user_agent, str_2_timestamp
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, subscribe_redis_msg, get_user_info
from user_manager import get_user_date_status, set_user_date_status, spain_user_field
from extension.session_manager import create_session

queue_dict = {}


# @timmer
def date_job(user):
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()

    # 登录状态为否 跳过等待10s用户登陆
    # isVIP = user["dateVIP"]
    # user = get_user_info(user)
    # user["dateVIP"] = isVIP
    if not user.get("is_login", False):
        logger.error(f"##预约## 用户未登录:{user.get('chnname')}-{user.get('email')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}")
        time.sleep(0.1)
        return None, None

    logger.info(f"##预约## 开始预约: {user.get('chnname')}-{user.get('email')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}-{user.get('dateVIP')}")

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    proxy_dict = {"http": proxy, "https": proxy}
    session = session = requests.session()
    session.proxies.update(proxy_dict)
    session.cookies.update(cookie_dict)
    session.headers.update(header)  # 将重试策略应用到会话对象

    # 查询预约号
    flag_book, done_job = date_appointment(user, session)
    if not flag_book:
        user["is_login"] = False
        save_user_2_redis_queue(user)
    return flag_book, done_job


def thread_worker(queue_tag):
    current_queue = queue_dict[queue_tag]
    while not current_queue.empty():
        user_old = current_queue.get()
        isVIP = user_old["dateVIP"]
        user = get_user_info(user_old)
        user["dateVIP"] = isVIP
        if user and not get_user_date_status(user):
            set_user_date_status(user, True)  # 设置预约中状态，防止重复开始预约
            _, done_job = date_job(user)
            set_user_date_status(user, False)  # 处理完 把预约中状态改回去
        current_queue.task_done()


def date_avaliable(user_info, all_dates):
    if len(all_dates) == 0:
        return True
    start_date = user_info.get("startDate", "").replace("/", "-")
    end_date = user_info.get("endDate", "").replace("/", "-")
    # 判断出是否有可预约日期
    date_avaliable_flag = False
    for date_item in all_dates:
        if str_2_timestamp(start_date) <= str_2_timestamp(date_item) <= str_2_timestamp(end_date):
            date_avaliable_flag = True
            break
    return date_avaliable_flag


def start_date_appointment(channel, msg_str, thread_count=15):
    msg_json = json.loads(msg_str)
    center_code = msg_json.get("centerCode")  # 放号地区
    isVIP = msg_json.get("isVIP", False)  # 是否是是VIP号
    open_days = msg_json.get("dates")  # 开放日期数组
    visaType = msg_json.get("visaType")

    # 已经开始了就不接受redis消息了
    logger.info(f"##预约#收到redis放号通知: 开始预约 {center_code} 地区用户, visaType: {visaType}, VIP:{isVIP}, 可约日期:{open_days}")

    # 每个地区单独的队列，防止客户重复预约
    queue_tag = f"{center_code}-{visaType}-{'vip' if isVIP else 'normal'}"
    if not queue_dict.get(queue_tag):
        queue_dict[queue_tag] = Queue()

    current_queue = queue_dict.get(queue_tag)

    all_users = get_users_with_queue_name(spain_user_field)  # brazil_faker_app_users spain_user_field
    # all_users = get_users_with_queue_name("brazil_faker_app_users") # 测试用户队列
    # 筛选等待预约的客户
    users_wating_appointment = list(filter(lambda u: u["status"] == "update_appointment" and u.get("centerCode") == center_code and u.get("visaTypeCode") == visaType, all_users))
    # VIP等候室只预约接受VIP的，普通号全部预约
    if isVIP:
        users_wating_appointment = list(filter(lambda u: u["acceptVIP"] == 1, users_wating_appointment))

    if len(users_wating_appointment) <= 0:
        logger.info(f"##预约#{center_code} 地区无用户等待预约")
        return []

    # 把期望日期有的用户筛选出来加入队列预约
    for user in users_wating_appointment:
        if date_avaliable(user, open_days):
            user["dateVIP"] = isVIP  # 增加一个标记 表示这次预约的是否是VIP
            current_queue.put(user)

    if current_queue.empty():
        logger.info(f"##预约#{center_code} 地区无用户期望的日期")
        return []

    # 进行预约的用户数
    threads = []
    for _ in range(thread_count):
        thread = threading.Thread(target=thread_worker, args=(queue_tag,), daemon=True)
        threads.append(thread)
        thread.start()

    return threads


def listen_redis_pub():
    logger.info("开启redis sub 订阅")

    # # 初始化重置用户的预约状态
    all_users = get_users_with_queue_name(spain_user_field)
    for user in all_users:
        set_user_date_status(user, False)

    subscribe_redis_msg(start_date_appointment)
    while True:
        logger.debug("redis_sub 订阅中...")
        time.sleep(600)


def date_single_users(centerCode="BEIJING"):
    # # 初始化重置用户的预约状态
    # all_users = get_users_with_queue_name(spain_user_field)
    # for user in all_users:
    #     set_user_date_status(user, False)

    info = {
        "centerCode": centerCode,
        "dates": ["2025-08-28", "2025-08-29"],
        "isVIP": True,
        "visaType": "Tourism",
    }
    str_info = json.dumps(info)

    while True:
        threads = start_date_appointment("channel", str_info, 1)
        for t in threads:
            t.join()
        time.sleep(5)


if __name__ == "__main__":
    # 获取外部参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if len(args) > 0 else 0
    if thread_count:
        listen_redis_pub()
    else:  # 本地调试时
        date_single_users("NANJING")
