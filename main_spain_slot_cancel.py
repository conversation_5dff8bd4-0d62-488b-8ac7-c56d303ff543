#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版西班牙签证预约取消模块
主要优化点：
1. 使用线程池替代手动线程管理
2. 简化函数结构，去掉复杂的类封装
3. 改进错误处理和日志记录
4. 优化资源管理和会话处理
"""

import requests
import time
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from copy import deepcopy
from bs4 import BeautifulSoup
from typing import Dict, List, Optional

# 导入现有模块
from spain_visa_login import user_login
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent, send_dd_msg, send_wx_msg, update_booking_status, BOOK_STATUS
from config import headers, url_host
from user_manager import get_email_otp, save_user_2_redis_queue, get_users_with_queue_name, move_user_2_queue
from user_manager import spain_success_users, spain_error_users, spain_slot_cancel_users, del_user_from_redis
from bypass_verify_code import bypass_verify_code_pipeline

# 配置常量
DEFAULT_THREAD_COUNT = 4
SLEEP_INTERVAL = 10
LOGIN_TIMEOUT = 600  # 10分钟
SESSION_TIMEOUT = 15
EMAIL_WAIT_TIME = 2


def create_session_with_proxy() -> Optional[requests.Session]:
    """创建带代理的会话"""
    try:
        session = requests.Session()
        header = deepcopy(headers)
        header["user-agent"] = get_random_user_agent()
        session.headers = header

        proxy = get_new_proxy()
        if not proxy:
            logger.error("#取消预约# 无法获取代理")
            return None

        proxy_dict = {"http": proxy, "https": proxy}
        session.proxies.update(proxy_dict)
        session.proxy_url = proxy

        return session
    except Exception as e:
        logger.error(f"#取消预约# 创建会话失败: {e}")
        return None


def validate_user_for_cancel(user: Dict) -> bool:
    """验证用户是否可以取消预约"""
    if not user.get("appointment_no"):
        send_dd_msg(f"#取消预约# 失败: {user.get('chnname')} {user.get('email')} {user.get('centerCode')}, appointment_no不存在")
        send_wx_msg(f"#取消预约# 失败: {user.get('chnname')} {user.get('email')} {user.get('centerCode')}, appointment_no不存在")
        user.pop('pay_qrcode', None)
        user.pop('cookies', None)
        user.pop('proxy', None)
        logger.error(f"#取消预约# appointment_no不存在: {str(user)}")
        del_user_from_redis(user)
        return False

    required_fields = ["email", "chnname", "passportNO", "centerCode"]
    missing_fields = [field for field in required_fields if not user.get(field)]

    if missing_fields:
        logger.error(f"#取消预约# 用户数据不完整，缺少字段: {missing_fields}")
        return False

    return True


def ensure_user_login(user: Dict, session: requests.Session) -> bool:
    """确保用户已登录"""
    user_id = f"{user.get('chnname')}-{user.get('passportNO')}"

    # 检查登录状态是否有效
    if user.get("is_login", False) and int(time.time()) - int(user.get("updateTime", 0)) <= LOGIN_TIMEOUT:
        # 使用现有的代理和cookie
        proxy = user.get("proxy")
        cookies = user.get("cookies")

        if proxy:
            proxy_dict = {"http": proxy, "https": proxy}
            session.proxies.update(proxy_dict)
        if cookies:
            session.cookies.update(cookies)

        logger.debug(f"#取消预约# 使用现有登录状态: {user_id}")
        return True

    # 需要重新登录
    logger.debug(f"正在重新登录: {user_id}")

    flag_login, info_login = user_login(user, session)

    if flag_login:
        logger.info(f"#取消预约# 登录成功: {user_id}")
        user["cookies"] = session.cookies.get_dict()
        user["proxy"] = session.proxy_url
        user["is_login"] = True
        user["updateTime"] = int(time.time())
    else:
        logger.error(f"#取消预约# 登录失败: {user_id}, 错误: {info_login}")
        user["is_login"] = False
        user["updateTime"] = int(time.time())

    # 更新用户状态到Redis
    save_user_2_redis_queue(user)
    return flag_login


def cancel_appointment_request(user: Dict, session: requests.Session) -> bool:
    """执行预约取消请求"""
    try:
        user_id = f"{user.get('chnname')}-{user.get('passportNO')}"

        # 1. 获取取消页面
        url_cancel_page = url_host + "/CHN/appointmentdata/BLSCancelAppointment"
        res_cancel_page = session.get(url_cancel_page, verify=False, timeout=SESSION_TIMEOUT)

        if res_cancel_page.status_code != 200:
            logger.error(f"#取消预约# 获取取消页面失败 {user_id}: {res_cancel_page.status_code}")
            return False

        # 2. 解析表单数据
        soup = BeautifulSoup(res_cancel_page.text, "html.parser")
        form = soup.find("form")
        if not form:
            logger.error(f"#取消预约# 未找到取消表单 {user_id}")
            return False

        form_data = {inp.get("name"): inp.get("value") for inp in form.select("input")}
        form_data.pop(None, None)
        form_data.update({"X-Requested-With": "XMLHttpRequest", "AppointmentNo": user.get("appointment_no", ""), "Email": user.get("email")})

        # 3. 提交取消请求
        url_manage_cancel = url_host + "/CHN/AppointmentData/ManageBLSCancelAppointment"
        res_post_cancel = session.post(url_manage_cancel, data=form_data, verify=False, timeout=SESSION_TIMEOUT)

        if res_post_cancel.status_code != 200:
            logger.error(f"#取消预约# 提交取消请求失败 {user_id}: {res_post_cancel.status_code}")
            return False

        response_data = res_post_cancel.json()
        if not response_data.get("success"):
            logger.error(f"#取消预约# 取消请求被拒绝 {user_id}: {response_data}")
            send_wx_msg(f"#取消预约# 取消预约失败: {user_id}: {response_data}")
            return False  # 可能已经取消过了

        # 4. 处理邮箱验证
        email_verification_code = response_data.get("emailverificationCode")
        app_data = response_data.get("appData")

        if not email_verification_code or not app_data:
            logger.error(f"#取消预约# 未获取到验证数据 {user_id}")
            return False

        # 等待邮箱验证码
        time.sleep(EMAIL_WAIT_TIME)
        email_otp = get_email_otp(user["email"])
        if not email_otp:
            logger.error(f"#取消预约# 未获取到邮箱验证码 {user_id}")
            return False

        # 5. 验证邮箱
        url_verify_email = url_host + f"/CHN/AppointmentData/VerifyEmailForCancelAppointment" f"?code={email_otp}&codeData={email_verification_code}"

        session.headers.update({"requestverificationtoken": form_data["__RequestVerificationToken"], "X-Requested-With": "XMLHttpRequest"})

        res_verify = session.post(url_verify_email, verify=False, timeout=SESSION_TIMEOUT)
        if res_verify.status_code != 200 or not res_verify.json().get("success"):
            logger.error(f"#取消预约# 邮箱验证失败 {user_id}: {res_verify.text}")
            return False

        # 6. 获取最终取消页面
        url_final_cancel = url_host + f"/CHN/AppointmentData/CancelAppointment?data={app_data}"
        res_cancel_final = session.get(url_final_cancel, verify=False, timeout=SESSION_TIMEOUT)

        if res_cancel_final.status_code != 200:
            logger.error(f"#取消预约# 获取最终取消页面失败 {user_id}: {res_cancel_final.status_code}")
            return False

        # 7. 处理验证码
        res_verify_captcha, _ = bypass_verify_code_pipeline(session, res_cancel_final.text)
        if not res_verify_captcha:
            logger.error(f"#取消预约# 验证码处理失败 {user_id}")
            return False

        # 8. 提交最终取消确认
        final_soup = BeautifulSoup(res_cancel_final.text, "html.parser")
        final_form = final_soup.find("form")
        if not final_form:
            logger.error(f"#取消预约# 未找到最终确认表单 {user_id}")
            return False

        final_form_data = {inp.get("name"): inp.get("value") for inp in final_form.select("input")}
        final_form_data.update({"captchaId": res_verify_captcha["captchaId"], "CancelReason": "日期选错了"})

        url_confirm_cancel = url_host + "/CHN/AppointmentData/CancelAppointment"
        res_confirm = session.post(url_confirm_cancel, data=final_form_data, verify=False, timeout=SESSION_TIMEOUT)

        if res_confirm.status_code == 200 and res_confirm.json().get("success"):
            success_msg = f"#取消预约# {user['chnname']} | {user['email']} | " f"{user['passportNO']} {user.get('centerCode')} 预约取消成功"
            logger.success(success_msg)

            if user.get("queue_name") == spain_success_users:
                send_wx_msg(success_msg)
            send_dd_msg(success_msg)
            return True
        else:
            error_msg = f"#取消预约# {user['chnname']}:{user['email']}-{user['passportNO']}:" f"{user['centerCode']}, 预约取消失败:{res_confirm.text}"
            logger.error(error_msg)
            send_dd_msg(error_msg)
            return False

    except Exception as e:
        logger.error(f"#取消预约# 取消预约流程异常 {user_id}: {e}")
        return False


def process_user_cancellation(user: Dict) -> bool:
    """处理单个用户的预约取消"""
    user_id = f"{user.get('chnname')}-{user.get('passportNO')}"

    try:
        # 1. 验证用户数据
        if not validate_user_for_cancel(user):
            return False

        # 2. 创建会话
        session = create_session_with_proxy()
        if not session:
            logger.error(f"创建会话失败 {user_id}")
            return False

        # 3. 确保用户已登录
        if not ensure_user_login(user, session):
            logger.error(f"用户登录失败 {user_id}")
            return False

        # 4. 执行取消操作
        cancel_success = cancel_appointment_request(user, session)

        # 5. 更新用户状态
        user["is_login"] = cancel_success
        if cancel_success:
            user["status"] = "canceled"
            move_user_2_queue(user, spain_error_users)
            update_booking_status(user, BOOK_STATUS.APPOINTMENT_CANCELED, "预约已取消")

        logger.info(f"#取消预约# 预约取消{'成功' if cancel_success else '失败'}: {user_id}")
        return cancel_success

    except Exception as e:
        logger.error(f"处理用户取消异常 {user_id}: {e}")
        return False
    finally:
        try:
            session.close()
        except:
            pass


def get_users_to_cancel() -> List[Dict]:
    """获取需要取消预约的用户列表"""
    try:
        all_users = get_users_with_queue_name(spain_slot_cancel_users)
        return [user for user in all_users if user.get("status") == "ok"]
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        return []


def main_cancellation_pipeline(thread_count: int = DEFAULT_THREAD_COUNT):
    """主取消流程管道"""
    logger.info(f"#取消预约# 启动西班牙预约取消服务，线程数: {thread_count}")

    with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="CancelWorker") as executor:
        while True:
            try:
                work_start = time.time()

                # 获取需要取消的用户
                users_to_cancel = get_users_to_cancel()

                if not users_to_cancel:
                    logger.debug("没有需要取消预约的用户")
                    time.sleep(30)
                    continue

                # 为每个用户设置队列名
                for user in users_to_cancel:
                    user["queue_name"] = spain_slot_cancel_users

                # 提交任务到线程池
                futures = [executor.submit(process_user_cancellation, user) for user in users_to_cancel]

                # 等待所有任务完成
                success_count = 0
                failed_count = 0

                for future in as_completed(futures):
                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        if result:
                            success_count += 1
                        else:
                            failed_count += 1
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"取消任务执行异常: {e}")

                elapsed_time = int(time.time() - work_start)
                logger.debug(f"#预约取消# 成功{success_count}个，失败{failed_count}个，" f"总计{len(users_to_cancel)}个用户，耗时: {elapsed_time}s")

                time.sleep(SLEEP_INTERVAL)

            except KeyboardInterrupt:
                logger.info("收到中断信号，正在关闭...")
                break
            except Exception as e:
                logger.error(f"主流程异常: {e}")
                time.sleep(SLEEP_INTERVAL)


if __name__ == "__main__":
    # 获取命令行参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if args and args[0].isdigit() else DEFAULT_THREAD_COUNT

    logger.info(f"开始取消用户预约，线程数: {thread_count}")
    main_cancellation_pipeline(thread_count)
